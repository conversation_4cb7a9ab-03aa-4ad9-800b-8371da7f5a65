# Contributing Guidelines

Thanks you so much for your interest in contributing to this project!

## About our deal

Hi! I'm <PERSON> and I'm the creator and maintainer of this project.

If you encounter bugs, please **do** open an issue describing the bug and including steps to easily reproduce it.

If you have an idea for an enhancement, go ahead and share it via an issue, but please don't expect a timely response.

This project is MIT-licensed, and this means that you can implement and use whatever enhancements you'd like.

## Commits and Code Padronization

This project follow the [Conventinal Commits](https://www.conventionalcommits.org/en/v1.0.0/) specification, and the following [Eslint Lint Rules](https://github.com/RafaelGoulartB/Next.js-Ecommerce/blob/master/.eslintrc.js).

## Bug reports

If you encounter a problem with this project, please open an issue. Be sure to include:

- Package version
- Node and Express versions
- Brief but thorough description of the issue
