{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["plugin:react/recommended", "standard", "plugin:@typescript-eslint/recommended", "prettier/@typescript-eslint", "prettier/standard", "prettier/recommend"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "prettier"], "rules": {"pettier/prettier": "error", "space-before-function-paren": "off", "react/prop-types": "off"}}