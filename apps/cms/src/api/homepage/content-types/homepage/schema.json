{"kind": "singleType", "collectionName": "homepages", "info": {"singularName": "homepage", "pluralName": "homepages", "displayName": "Homepage"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"Hero": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "homepage.hero", "repeatable": false}, "Highlights": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "homepage.highlights", "repeatable": false}, "About": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "homepage.about", "repeatable": false}, "Projects": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "homepage.projects-teasers", "repeatable": false}, "Partners": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "homepage.partners", "repeatable": false}, "Services": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "homepage.services-teaser", "repeatable": false}, "Testimonials": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "homepage.testimonials", "repeatable": false}, "Contact": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "homepage.contact-cta", "repeatable": false}}}