{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "Page"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true, "unique": true}, "Content": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["content.page-content"]}}}