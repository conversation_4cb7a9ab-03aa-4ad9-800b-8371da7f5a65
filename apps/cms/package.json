{"name": "cms", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.25.0", "@strapi/plugin-users-permissions": "5.25.0", "@strapi/strapi": "5.25.0", "mysql2": "^3.15.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-navigation": "^3.2.4", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "c7cc922f62714e383c34e39c4a2395bc70f4663bea931d09e5f3eb12307c18dc"}}