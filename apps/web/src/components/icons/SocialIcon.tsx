import React from "react";

export type SocialKind =
  | "facebook"
  | "twitter"
  | "instagram"
  | "linkedin"
  | "phone"
  | "email"
  | "google"
  | "youtube";

export function SocialIcon({ name, className }: { name: SocialKind | string; className?: string }) {
  switch (name) {
    case "facebook":
      return (
      <svg viewBox="0 0 320 512" className={className} fill="currentColor"><path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/></svg>      );
    case "twitter":
      return (
        <svg viewBox="0 0 512 512" className={className} fill="currentColor"><path d="M459.37 151.716a158.4 158.4 0 01-45.5 12.482 79.72 79.72 0 0034.952-43.962 158.76 158.76 0 01-50.514 19.312 79.33 79.33 0 00-135.1 72.253 225.08 225.08 0 01-163.36-82.86 79.33 79.33 0 0024.55 105.88 78.89 78.89 0 01-35.93-9.92v.997a79.32 79.32 0 0063.6 77.77 79.7 79.7 0 01-35.73 1.356 79.35 79.35 0 0074.06 55.08A158.9 158.9 0 0148 407.4a224.7 224.7 0 00121.66 35.68c146.2 0 226.22-121.12 226.22-226.22 0-3.45-.08-6.87-.23-10.27a161.6 161.6 0 0039.72-41.852z"/></svg>
      );
    case "instagram":
      return (
        <svg viewBox="0 0 448 512" className={className} fill="currentColor"><path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/></svg>
      );
    case "linkedin":
      return (
        <svg viewBox="0 0 448 512" className={className} fill="currentColor"><path d="M100.28 448H7.4V149.9h92.88zm-46.44-338C24.1 110 0 85.9 0 56.72A56.72 56.72 0 1153.84 0 56.72 56.72 0 0153.84 110zM447.9 448h-92.68V302.4c0-34.7-.7-79.3-48.34-79.3-48.4 0-55.82 37.8-55.82 76.9V448h-92.7V149.9h88.98v40.7h1.3c12.4-23.5 42.6-48.3 87.8-48.3 94 0 111.3 61.9 111.3 142.3V448z"/></svg>
      );
    case "phone":
      return (
        <svg viewBox="0 0 512 512" className={className} fill="currentColor"><path d="M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 14.1L329 103.2c-4.1 9.4-1.4 20.4 6.7 27.1l60.6 49.6c-36.6 77.9-96.4 137.7-174.3 174.3l-49.6-60.6c-6.7-8.1-17.7-10.8-27.1-6.7L49.5 361.9c-10.8 4.6-16.7 16.2-14.1 27.5l24 104c2.6 11.3 12.7 19.6 24.3 19.6 256.1 0 464-207.9 464-464 0-11.6-8.1-21.7-19.6-24.3z"/></svg>
      );
    case "email":
      return (
        <svg viewBox="0 0 512 512" className={className} fill="currentColor"><path d="M502.3 190.8l-192 160c-11.3 9.4-27.9 9.4-39.2 0l-192-160C65.3 179.8 64 164 72.8 152.6s24-14.4 35.2-5l182.4 152 182.4-152c11.2-9.4 27.9-7.8 36.7 3.6 8.9 11.3 7.2 27.9-4.8 38.6zM64 128h384c17.7 0 32 14.3 32 32v192c0 17.7-14.3 32-32 32H64c-17.7 0-32-14.3-32-32V160c0-17.7 14.3-32 32-32z"/></svg>
      );
    case "google":
      return (
        <svg viewBox="0 0 488 512" className={className} fill="currentColor"><path d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"/></svg>
      );
    case "youtube":
      return (
        <svg viewBox="0 0 576 512" className={className} fill="currentColor"><path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"/></svg>
      );
    default:
      return null;
  }
}

